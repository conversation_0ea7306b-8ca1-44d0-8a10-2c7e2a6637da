package com.lysjk.config;

import com.lysjk.config.typehandler.JsonMapTypeHandler;
import com.lysjk.config.typehandler.JsonStringTypeHandler;
import com.lysjk.config.typehandler.MultiPolygonTypeHandler;
import com.lysjk.config.typehandler.PointTypeHandler;
import com.lysjk.config.typehandler.PolygonTypeHandler;
import lombok.extern.slf4j.Slf4j;
import org.apache.ibatis.session.SqlSessionFactory;
import org.apache.ibatis.type.TypeHandlerRegistry;
import org.locationtech.jts.geom.MultiPolygon;
import org.locationtech.jts.geom.Point;
import org.locationtech.jts.geom.Polygon;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.ApplicationArguments;
import org.springframework.boot.ApplicationRunner;
import org.springframework.core.annotation.Order;
import org.springframework.stereotype.Component;

import java.util.Map;

/**
 * 优化的MyBatis配置
 * 在应用启动后注册PostGIS TypeHandler，解决JSON序列化深度嵌套问题
 */
@Slf4j
@Component
@Order(1) // 确保在其他组件之前执行
public class SimpleMyBatisConfig implements ApplicationRunner {

    @Autowired(required = false)
    private SqlSessionFactory sqlSessionFactory;

    @Override
    public void run(ApplicationArguments args) throws Exception {
        log.info("开始初始化PostGIS TypeHandler配置...");

        if (sqlSessionFactory != null) {
            registerTypeHandlers();
            log.info("PostGIS TypeHandler配置完成");
        } else {
            log.warn("SqlSessionFactory未找到，无法注册TypeHandler");
        }
    }

    private void registerTypeHandlers() {
        try {
            TypeHandlerRegistry registry = sqlSessionFactory.getConfiguration().getTypeHandlerRegistry();

            // 注册Point类型处理器
            registry.register(Point.class, PointTypeHandler.class);
            registry.register(Point.class, org.apache.ibatis.type.JdbcType.OTHER, PointTypeHandler.class);

            // 注册MultiPolygon类型处理器
            registry.register(MultiPolygon.class, MultiPolygonTypeHandler.class);
            registry.register(MultiPolygon.class, org.apache.ibatis.type.JdbcType.OTHER, MultiPolygonTypeHandler.class);

            // 注册Polygon类型处理器
            registry.register(Polygon.class, PolygonTypeHandler.class);
            registry.register(Polygon.class, org.apache.ibatis.type.JdbcType.OTHER, PolygonTypeHandler.class);

            // 注册JSON Map类型处理器
            registry.register(Map.class, JsonMapTypeHandler.class);
            registry.register(Map.class, org.apache.ibatis.type.JdbcType.OTHER, JsonMapTypeHandler.class);

            // 注册JSON字符串类型处理器
            registry.register(JsonStringTypeHandler.class);

            log.info("✓ PostGIS TypeHandler注册成功");

            // 验证注册结果
            var pointHandler = registry.getTypeHandler(Point.class);
            var multiPolygonHandler = registry.getTypeHandler(MultiPolygon.class);
            var polygonHandler = registry.getTypeHandler(Polygon.class);

            if (pointHandler instanceof PointTypeHandler) {
                log.info("✓ Point TypeHandler: {}", pointHandler.getClass().getSimpleName());
            } else {
                log.warn("✗ Point TypeHandler注册失败，当前类型: {}",
                        pointHandler != null ? pointHandler.getClass().getSimpleName() : "null");
            }

            if (multiPolygonHandler instanceof MultiPolygonTypeHandler) {
                log.info("✓ MultiPolygon TypeHandler: {}", multiPolygonHandler.getClass().getSimpleName());
            } else {
                log.warn("✗ MultiPolygon TypeHandler注册失败，当前类型: {}",
                        multiPolygonHandler != null ? multiPolygonHandler.getClass().getSimpleName() : "null");
            }

            if (polygonHandler instanceof PolygonTypeHandler) {
                log.info("✓ Polygon TypeHandler: {}", polygonHandler.getClass().getSimpleName());
            } else {
                log.warn("✗ Polygon TypeHandler注册失败，当前类型: {}",
                        polygonHandler != null ? polygonHandler.getClass().getSimpleName() : "null");
            }

        } catch (Exception e) {
            log.error("TypeHandler注册失败", e);
            throw new RuntimeException("PostGIS TypeHandler注册失败", e);
        }
    }
}

//package com.lysjk.config;
//
//import com.lysjk.config.typehandler.MultiPolygonTypeHandler;
//import com.lysjk.config.typehandler.PointTypeHandler;
//import lombok.extern.slf4j.Slf4j;
//import org.apache.ibatis.session.SqlSessionFactory;
//import org.apache.ibatis.type.TypeHandlerRegistry;
//import org.locationtech.jts.geom.MultiPolygon;
//import org.locationtech.jts.geom.Point;
//import org.springframework.beans.factory.annotation.Autowired;
//import org.springframework.boot.ApplicationArguments;
//import org.springframework.boot.ApplicationRunner;
//import org.springframework.core.annotation.Order;
//import org.springframework.stereotype.Component;
//
///**
// * 优化的MyBatis配置
// * 在应用启动后注册PostGIS TypeHandler，解决JSON序列化深度嵌套问题
// */
//@Slf4j
//@Component
//@Order(1) // 确保在其他组件之前执行
//public class SimpleMyBatisConfig implements ApplicationRunner {
//
//    @Autowired(required = false)
//    private SqlSessionFactory sqlSessionFactory;
//
//    @Override
//    public void run(ApplicationArguments args) throws Exception {
//        log.info("开始初始化PostGIS TypeHandler配置...");
//
//        if (sqlSessionFactory != null) {
//            registerTypeHandlers();
//            log.info("PostGIS TypeHandler配置完成");
//        } else {
//            log.warn("SqlSessionFactory未找到，无法注册TypeHandler");
//        }
//    }
//
//    private void registerTypeHandlers() {
//        try {
//            TypeHandlerRegistry registry = sqlSessionFactory.getConfiguration().getTypeHandlerRegistry();
//
//            // 注册Point类型处理器
//            registry.register(Point.class, PointTypeHandler.class);
//            registry.register(Point.class, org.apache.ibatis.type.JdbcType.OTHER, PointTypeHandler.class);
//
//            // 注册MultiPolygon类型处理器
//            registry.register(MultiPolygon.class, MultiPolygonTypeHandler.class);
//            registry.register(MultiPolygon.class, org.apache.ibatis.type.JdbcType.OTHER, MultiPolygonTypeHandler.class);
//
//            log.info("✓ PostGIS TypeHandler注册成功");
//
//            // 验证注册结果
//            var pointHandler = registry.getTypeHandler(Point.class);
//            var multiPolygonHandler = registry.getTypeHandler(MultiPolygon.class);
//
//            if (pointHandler instanceof PointTypeHandler) {
//                log.info("✓ Point TypeHandler: {}", pointHandler.getClass().getSimpleName());
//            } else {
//                log.warn("✗ Point TypeHandler注册失败，当前类型: {}",
//                    pointHandler != null ? pointHandler.getClass().getSimpleName() : "null");
//            }
//
//            if (multiPolygonHandler instanceof MultiPolygonTypeHandler) {
//                log.info("✓ MultiPolygon TypeHandler: {}", multiPolygonHandler.getClass().getSimpleName());
//            } else {
//                log.warn("✗ MultiPolygon TypeHandler注册失败，当前类型: {}",
//                    multiPolygonHandler != null ? multiPolygonHandler.getClass().getSimpleName() : "null");
//            }
//
//        } catch (Exception e) {
//            log.error("TypeHandler注册失败", e);
//            throw new RuntimeException("PostGIS TypeHandler注册失败", e);
//        }
//    }
//}
